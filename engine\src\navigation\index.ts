/**
 * 导航模块
 *
 * 导航模块提供了一套完整的导航系统，用于处理角色在3D场景中的移动和寻路。
 * 该模块支持导航网格生成、路径规划、动态避障等功能，特别针对数字人路径编辑和跟随进行了优化。
 *
 * 主要功能：
 * - 导航网格生成和管理
 * - A*路径规划算法
 * - 动态避障
 * - 路径平滑
 * - 群体导航
 * - 数字人路径编辑和跟随
 * - 路径验证和优化
 * - 路径插值和动画
 *
 * 使用示例：
 * ```typescript
 * import { AvatarPathSystem, AvatarPath, PathPoint, LoopMode, InterpolationType } from '../navigation';
 * import * as THREE from 'three';
 *
 * // 创建数字人路径系统
 * const avatarPathSystem = new AvatarPathSystem({
 *   debug: true,
 *   enableValidation: true,
 *   enablePerformanceMonitoring: true
 * });
 *
 * // 添加路径系统到世界
 * world.addSystem(avatarPathSystem);
 *
 * // 创建路径
 * const path = new AvatarPath({
 *   name: '巡逻路径',
 *   avatarId: 'avatar_001',
 *   loopMode: LoopMode.LOOP,
 *   interpolation: InterpolationType.SMOOTH
 * });
 *
 * // 添加路径点
 * path.addPoint(new PathPoint(new THREE.Vector3(0, 0, 0), {
 *   waitTime: 2,
 *   speed: 1.5,
 *   animation: 'idle'
 * }));
 *
 * path.addPoint(new PathPoint(new THREE.Vector3(10, 0, 0), {
 *   waitTime: 1,
 *   speed: 2.0,
 *   animation: 'walk'
 * }));
 *
 * // 为实体创建路径
 * const pathComponent = avatarPathSystem.createPathForEntity(avatarEntity, path.toJSON());
 *
 * // 开始路径
 * avatarPathSystem.startEntityPath(avatarEntity.id);
 * ```
 */

// 导入必要的类型和类
import * as THREE from 'three';
import { AvatarPath } from './path/AvatarPath';
import { PathPoint } from './path/PathPoint';
import { LoopMode, InterpolationType } from './types';

// 导出路径相关类
export { AvatarPath } from './path/AvatarPath';
export { PathPoint } from './path/PathPoint';
export { PathInterpolator } from './path/PathInterpolator';
export { PathValidator } from './path/PathValidator';

// 导出组件
export { AvatarPathComponent } from './components/AvatarPathComponent';
export { PathFollowingComponent, PathFollowingState } from './components/PathFollowingComponent';

// 导出系统
export { AvatarPathSystem } from './systems/AvatarPathSystem';

// 导出类型定义
export type {
  // 基础类型
  PathTrigger,
  PathMetadata,
  AvatarPathData,
  
  // 枚举类型
  LoopMode,
  InterpolationType,
  PathEventType,
  
  // 选项类型
  AvatarPathOptions,
  PathFollowingOptions,
  NavigationSystemOptions,
  NavigationMeshOptions,
  PathFinderOptions,
  NavigationAgentOptions,
  NavigationObstacleOptions,
  
  // 事件类型
  PathEventData,
  
  // 导航相关类型
  NavigationPath,
  NavigationNode,
  NavigationTriangle,
  NavigationEdge
} from './types';

// 导出验证相关
export type { ValidationResult, ValidationOptions } from './path/PathValidator';

/**
 * 创建简单的数字人路径
 * @param name 路径名称
 * @param avatarId 数字人ID
 * @param points 路径点位置数组
 * @param options 选项
 * @returns 路径实例
 */
export function createSimpleAvatarPath(
  name: string,
  avatarId: string,
  points: THREE.Vector3[],
  options: {
    speed?: number;
    waitTime?: number;
    animation?: string;
    loopMode?: LoopMode;
    interpolation?: InterpolationType;
  } = {}
): AvatarPath {
  const {
    speed = 1.0,
    waitTime = 0,
    animation = 'walk',
    loopMode = LoopMode.NONE,
    interpolation = InterpolationType.LINEAR
  } = options;

  const path = new AvatarPath({
    name,
    avatarId,
    loopMode,
    interpolation
  });

  // 添加路径点
  points.forEach((position, index) => {
    const pathPoint = new PathPoint(position, {
      speed,
      waitTime: index === 0 || index === points.length - 1 ? waitTime : 0, // 只在起点和终点等待
      animation
    });
    path.addPoint(pathPoint);
  });

  return path;
}

/**
 * 创建巡逻路径
 * @param name 路径名称
 * @param avatarId 数字人ID
 * @param center 中心点
 * @param radius 半径
 * @param pointCount 路径点数量
 * @param options 选项
 * @returns 路径实例
 */
export function createPatrolPath(
  name: string,
  avatarId: string,
  center: THREE.Vector3,
  radius: number,
  pointCount: number = 4,
  options: {
    speed?: number;
    waitTime?: number;
    animation?: string;
    interpolation?: InterpolationType;
  } = {}
): AvatarPath {
  const {
    speed = 1.0,
    waitTime = 1.0,
    animation = 'walk',
    interpolation = InterpolationType.SMOOTH
  } = options;

  const points: THREE.Vector3[] = [];
  
  // 生成圆形巡逻点
  for (let i = 0; i < pointCount; i++) {
    const angle = (i / pointCount) * Math.PI * 2;
    const x = center.x + Math.cos(angle) * radius;
    const z = center.z + Math.sin(angle) * radius;
    points.push(new THREE.Vector3(x, center.y, z));
  }

  return createSimpleAvatarPath(name, avatarId, points, {
    speed,
    waitTime,
    animation,
    loopMode: LoopMode.LOOP,
    interpolation
  });
}

/**
 * 创建往返路径
 * @param name 路径名称
 * @param avatarId 数字人ID
 * @param startPoint 起点
 * @param endPoint 终点
 * @param options 选项
 * @returns 路径实例
 */
export function createBackAndForthPath(
  name: string,
  avatarId: string,
  startPoint: THREE.Vector3,
  endPoint: THREE.Vector3,
  options: {
    speed?: number;
    waitTime?: number;
    animation?: string;
    interpolation?: InterpolationType;
  } = {}
): AvatarPath {
  const {
    speed = 1.0,
    waitTime = 2.0,
    animation = 'walk',
    interpolation = InterpolationType.LINEAR
  } = options;

  return createSimpleAvatarPath(name, avatarId, [startPoint, endPoint], {
    speed,
    waitTime,
    animation,
    loopMode: LoopMode.PINGPONG,
    interpolation
  });
}

/**
 * 路径工具函数
 */
export const PathUtils = {
  /**
   * 计算两点之间的距离
   */
  distance: (point1: THREE.Vector3, point2: THREE.Vector3): number => {
    return point1.distanceTo(point2);
  },

  /**
   * 计算路径总长度
   */
  calculatePathLength: (points: THREE.Vector3[]): number => {
    let totalLength = 0;
    for (let i = 0; i < points.length - 1; i++) {
      totalLength += points[i].distanceTo(points[i + 1]);
    }
    return totalLength;
  },

  /**
   * 平滑路径点
   */
  smoothPath: (points: THREE.Vector3[], factor: number = 0.5): THREE.Vector3[] => {
    if (points.length < 3) return points;

    const smoothedPoints = [points[0]]; // 保持第一个点不变

    for (let i = 1; i < points.length - 1; i++) {
      const prev = points[i - 1];
      const current = points[i];
      const next = points[i + 1];

      // 计算平滑后的位置
      const smoothed = new THREE.Vector3()
        .addScaledVector(prev, 0.25 * factor)
        .addScaledVector(current, 1 - factor)
        .addScaledVector(next, 0.25 * factor);

      smoothedPoints.push(smoothed);
    }

    smoothedPoints.push(points[points.length - 1]); // 保持最后一个点不变
    return smoothedPoints;
  },

  /**
   * 简化路径（移除冗余点）
   */
  simplifyPath: (points: THREE.Vector3[], tolerance: number = 0.1): THREE.Vector3[] => {
    if (points.length < 3) return points;

    const simplified = [points[0]];

    for (let i = 1; i < points.length - 1; i++) {
      const prev = simplified[simplified.length - 1];
      const current = points[i];
      const next = points[i + 1];

      // 计算当前点到前后两点连线的距离
      const line = new THREE.Line3(prev, next);
      const closestPoint = new THREE.Vector3();
      line.closestPointToPoint(current, true, closestPoint);
      const distance = current.distanceTo(closestPoint);

      if (distance > tolerance) {
        simplified.push(current);
      }
    }

    simplified.push(points[points.length - 1]);
    return simplified;
  }
};

// 重新导出THREE.js中的Vector3，方便使用
export { Vector3 } from 'three';
