/**
 * 性能监控系统
 * 用于收集和分析性能指标
 */
import { Debug } from './Debug';

/**
 * 性能指标类型
 */
export enum PerformanceMetricType {
  /** 帧率 */
  FPS = 'fps',
  /** 帧时间 */
  FRAME_TIME = 'frameTime',
  /** 渲染时间 */
  RENDER_TIME = 'renderTime',
  /** 物理更新时间 */
  PHYSICS_TIME = 'physicsTime',
  /** 动画更新时间 */
  ANIMATION_TIME = 'animationTime',
  /** 输入处理时间 */
  INPUT_TIME = 'inputTime',
  /** 网络更新时间 */
  NETWORK_TIME = 'networkTime',
  /** 脚本执行时间 */
  SCRIPT_TIME = 'scriptTime',
  /** 总更新时间 */
  TOTAL_UPDATE_TIME = 'totalUpdateTime',
  /** 内存使用 */
  MEMORY_USAGE = 'memoryUsage',
  /** 纹理内存 */
  TEXTURE_MEMORY = 'textureMemory',
  /** 几何体内存 */
  GEOMETRY_MEMORY = 'geometryMemory',
  /** 绘制调用次数 */
  DRAW_CALLS = 'drawCalls',
  /** 三角形数量 */
  TRIANGLES = 'triangles',
  /** 顶点数量 */
  VERTICES = 'vertices',
  /** 碰撞对数量 */
  COLLISION_PAIRS = 'collisionPairs',
  /** 接触点数量 */
  CONTACT_POINTS = 'contactPoints',
  /** 实体数量 */
  ENTITY_COUNT = 'entityCount',
  /** 组件数量 */
  COMPONENT_COUNT = 'componentCount',
  /** 系统数量 */
  SYSTEM_COUNT = 'systemCount',
  /** GPU使用率 */
  GPU_USAGE = 'gpuUsage',
  /** GPU内存使用 */
  GPU_MEMORY = 'gpuMemory',
  /** CPU使用率 */
  CPU_USAGE = 'cpuUsage',
  /** 资源加载时间 */
  RESOURCE_LOAD_TIME = 'resourceLoadTime',
  /** 资源数量 */
  RESOURCE_COUNT = 'resourceCount',
  /** 资源内存使用 */
  RESOURCE_MEMORY = 'resourceMemory',
  /** 着色器编译时间 */
  SHADER_COMPILE_TIME = 'shaderCompileTime',
  /** 着色器数量 */
  SHADER_COUNT = 'shaderCount',
  /** 材质数量 */
  MATERIAL_COUNT = 'materialCount',
  /** 纹理数量 */
  TEXTURE_COUNT = 'textureCount',
  /** 几何体数量 */
  GEOMETRY_COUNT = 'geometryCount',
  /** 光源数量 */
  LIGHT_COUNT = 'lightCount',
  /** 阴影贴图数量 */
  SHADOW_MAP_COUNT = 'shadowMapCount',
  /** 后处理通道数量 */
  POST_PROCESS_PASS_COUNT = 'postProcessPassCount',
  /** 后处理时间 */
  POST_PROCESS_TIME = 'postProcessTime',
  /** 可见对象数量 */
  VISIBLE_OBJECT_COUNT = 'visibleObjectCount',
  /** 剔除对象数量 */
  CULLED_OBJECT_COUNT = 'culledObjectCount',
  /** 网络消息数量 */
  NETWORK_MESSAGE_COUNT = 'networkMessageCount',
  /** 网络数据大小 */
  NETWORK_DATA_SIZE = 'networkDataSize',
  /** 网络延迟 */
  NETWORK_LATENCY = 'networkLatency',
  /** 事件数量 */
  EVENT_COUNT = 'eventCount',
  /** 事件处理时间 */
  EVENT_PROCESSING_TIME = 'eventProcessingTime',
  /** 垃圾回收时间 */
  GC_TIME = 'gcTime',
  /** 垃圾回收次数 */
  GC_COUNT = 'gcCount',
  /** 自定义指标 */
  CUSTOM = 'custom',
}

/**
 * 性能指标
 */
export interface PerformanceMetric {
  /** 指标类型 */
  type: PerformanceMetricType;
  /** 指标名称 */
  name: string;
  /** 当前值 */
  value: number;
  /** 最小值 */
  min: number;
  /** 最大值 */
  max: number;
  /** 平均值 */
  average: number;
  /** 历史值 */
  history: number[];
  /** 历史长度限制 */
  historyLimit: number;
  /** 单位 */
  unit?: string;
  /** 阈值 */
  threshold?: number;
  /** 是否超过阈值 */
  exceedsThreshold?: boolean;
}

/**
 * 性能瓶颈类型
 */
export enum PerformanceBottleneckType {
  /** 无瓶颈 */
  NONE = 'none',
  /** CPU瓶颈 */
  CPU = 'cpu',
  /** GPU瓶颈 */
  GPU = 'gpu',
  /** 内存瓶颈 */
  MEMORY = 'memory',
  /** 网络瓶颈 */
  NETWORK = 'network',
  /** 渲染瓶颈 */
  RENDERING = 'rendering',
  /** 物理瓶颈 */
  PHYSICS = 'physics',
  /** 脚本瓶颈 */
  SCRIPT = 'script',
  /** 资源瓶颈 */
  RESOURCES = 'resources',
  /** 未知瓶颈 */
  UNKNOWN = 'unknown'
}

/**
 * 性能瓶颈
 */
export interface PerformanceBottleneck {
  /** 瓶颈类型 */
  type: PerformanceBottleneckType;
  /** 瓶颈严重程度 (0-1) */
  severity: number;
  /** 瓶颈描述 */
  description: string;
  /** 相关指标 */
  relatedMetrics: string[];
  /** 建议优化措施 */
  optimizationSuggestions: string[];
}

/**
 * 性能趋势类型
 */
export enum PerformanceTrendType {
  /** 稳定 */
  STABLE = 'stable',
  /** 改善 */
  IMPROVING = 'improving',
  /** 恶化 */
  DEGRADING = 'degrading',
  /** 波动 */
  FLUCTUATING = 'fluctuating',
  /** 未知 */
  UNKNOWN = 'unknown'
}

/**
 * 性能趋势
 */
export interface PerformanceTrend {
  /** 趋势类型 */
  type: PerformanceTrendType;
  /** 指标类型 */
  metricType: PerformanceMetricType;
  /** 变化率 */
  changeRate: number;
  /** 趋势开始时间 */
  startTime: number;
  /** 趋势持续时间 */
  duration: number;
}

/**
 * 性能报告
 */
export interface PerformanceReport {
  /** 报告时间 */
  timestamp: number;
  /** 性能指标 */
  metrics: { [key: string]: PerformanceMetric };
  /** 性能瓶颈 */
  bottlenecks: PerformanceBottleneck[];
  /** 性能趋势 */
  trends: PerformanceTrend[];
  /** 总体性能评分 (0-100) */
  overallScore: number;
  /** 性能状态 */
  status: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  /** 自定义数据 */
  customData?: { [key: string]: any };
}

/**
 * 性能监控配置
 */
export interface PerformanceMonitorConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 采样间隔（毫秒） */
  sampleInterval?: number;
  /** 历史长度限制 */
  historyLimit?: number;
  /** 是否启用自动采样 */
  autoSample?: boolean;
  /** 是否启用调试输出 */
  debug?: boolean;
  /** 是否启用性能警告 */
  enableWarnings?: boolean;
  /** 是否收集渲染指标 */
  collectRenderMetrics?: boolean;
  /** 是否收集物理指标 */
  collectPhysicsMetrics?: boolean;
  /** 是否收集内存指标 */
  collectMemoryMetrics?: boolean;
  /** 是否收集系统指标 */
  collectSystemMetrics?: boolean;
  /** 是否收集GPU指标 */
  collectGPUMetrics?: boolean;
  /** 是否收集CPU指标 */
  collectCPUMetrics?: boolean;
  /** 是否收集网络指标 */
  collectNetworkMetrics?: boolean;
  /** 是否收集资源指标 */
  collectResourceMetrics?: boolean;
  /** 是否收集事件指标 */
  collectEventMetrics?: boolean;
  /** 是否收集垃圾回收指标 */
  collectGCMetrics?: boolean;
  /** 是否启用瓶颈检测 */
  enableBottleneckDetection?: boolean;
  /** 是否启用趋势分析 */
  enableTrendAnalysis?: boolean;
  /** 是否启用性能评分 */
  enablePerformanceScoring?: boolean;
  /** 是否启用优化建议 */
  enableOptimizationSuggestions?: boolean;
  /** 是否启用自动优化 */
  enableAutoOptimization?: boolean;
  /** 是否启用性能报告导出 */
  enableReportExport?: boolean;
  /** 性能报告导出间隔（毫秒） */
  reportExportInterval?: number;
  /** 性能报告导出格式 */
  reportExportFormat?: 'json' | 'csv' | 'html';
  /** 性能报告导出路径 */
  reportExportPath?: string;
  /** 是否启用远程监控 */
  enableRemoteMonitoring?: boolean;
  /** 远程监控服务器URL */
  remoteMonitoringURL?: string;
  /** 远程监控认证令牌 */
  remoteMonitoringToken?: string;
  /** 远程监控数据发送间隔（毫秒） */
  remoteMonitoringSendInterval?: number;
}

/**
 * 性能监控系统
 */
export class PerformanceMonitor {
  /** 单例实例 */
  private static instance: PerformanceMonitor;

  /** 配置 */
  private config: Required<PerformanceMonitorConfig>;
  /** 性能指标 */
  private metrics: { [key: string]: PerformanceMetric } = {};
  /** 是否正在运行 */
  private running: boolean = false;
  /** 采样定时器ID */
  private sampleTimerId: number | null = null;
  /** 帧开始时间 */
  private frameStartTime: number = 0;
  /** 上一帧时间 */
  private lastFrameTime: number = 0;
  /** 帧计数 */
  private frameCount: number = 0;
  /** 测量开始时间映射 */
  private measureStartTimes: { [key: string]: number } = {};
  /** 性能瓶颈 */
  private bottlenecks: PerformanceBottleneck[] = [];
  /** 性能趋势 */
  private trends: PerformanceTrend[] = [];
  /** 上次分析时间 */
  private lastAnalysisTime: number = 0;
  /** 总体性能评分 */
  private overallScore: number = 100;
  /** 性能状态 */
  private performanceStatus: 'excellent' | 'good' | 'fair' | 'poor' | 'critical' = 'excellent';
  /** 报告导出定时器ID */
  private reportExportTimerId: number | null = null;
  /** 远程监控定时器ID */
  private remoteMonitoringTimerId: number | null = null;
  /** 瓶颈检测定时器ID */
  private bottleneckDetectionTimerId: number | null = null;
  /** 趋势分析定时器ID */
  private trendAnalysisTimerId: number | null = null;
  /** 性能历史数据 */
  private performanceHistory: PerformanceReport[] = [];
  /** 优化建议缓存 */
  private optimizationSuggestions: { [key: string]: string[] } = {};

  /**
   * 获取单例实例
   */
  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  /**
   * 私有构造函数
   */
  private constructor() {
    // 默认配置
    this.config = {
      enabled: true,
      sampleInterval: 1000,
      historyLimit: 60,
      autoSample: true,
      debug: false,
      enableWarnings: true,
      collectRenderMetrics: true,
      collectPhysicsMetrics: true,
      collectMemoryMetrics: true,
      collectSystemMetrics: true,
      collectGPUMetrics: true,
      collectCPUMetrics: true,
      collectNetworkMetrics: true,
      collectResourceMetrics: true,
      collectEventMetrics: true,
      collectGCMetrics: true,
      enableBottleneckDetection: true,
      enableTrendAnalysis: true,
      enablePerformanceScoring: true,
      enableOptimizationSuggestions: true,
      enableAutoOptimization: false,
      enableReportExport: false,
      reportExportInterval: 60000,
      reportExportFormat: 'json',
      reportExportPath: './performance-reports',
      enableRemoteMonitoring: false,
      remoteMonitoringURL: '',
      remoteMonitoringToken: '',
      remoteMonitoringSendInterval: 10000,
    };

    // 初始化基本指标
    this.initializeMetrics();

    // 初始化瓶颈和趋势分析
    this.initializeAnalytics();
  }

  /**
   * 初始化分析功能
   */
  private initializeAnalytics(): void {
    // 初始化瓶颈和趋势分析所需的数据结构
    this.bottlenecks = [];
    this.trends = [];
    this.lastAnalysisTime = Date.now();
    this.overallScore = 100;
    this.performanceStatus = 'excellent';
  }

  /**
   * 初始化指标
   */
  private initializeMetrics(): void {
    // 基本性能指标
    this.registerMetric(PerformanceMetricType.FPS, 'FPS', 0, 'fps', 30);
    this.registerMetric(PerformanceMetricType.RENDER_TIME, '渲染时间', 0, 'ms', 16);
    this.registerMetric(PerformanceMetricType.PHYSICS_TIME, '物理更新时间', 0, 'ms', 8);
    this.registerMetric(PerformanceMetricType.ANIMATION_TIME, '动画更新时间', 0, 'ms', 5);
    this.registerMetric(PerformanceMetricType.INPUT_TIME, '输入处理时间', 0, 'ms', 2);
    this.registerMetric(PerformanceMetricType.NETWORK_TIME, '网络更新时间', 0, 'ms', 5);
    this.registerMetric(PerformanceMetricType.SCRIPT_TIME, '脚本执行时间', 0, 'ms', 5);
    this.registerMetric(PerformanceMetricType.TOTAL_UPDATE_TIME, '总更新时间', 0, 'ms', 33);

    // 内存指标
    this.registerMetric(PerformanceMetricType.MEMORY_USAGE, '内存使用', 0, 'MB', 500);
    this.registerMetric(PerformanceMetricType.TEXTURE_MEMORY, '纹理内存', 0, 'MB', 200);
    this.registerMetric(PerformanceMetricType.GEOMETRY_MEMORY, '几何体内存', 0, 'MB', 100);

    // 渲染指标
    this.registerMetric(PerformanceMetricType.DRAW_CALLS, '绘制调用次数', 0, '', 1000);
    this.registerMetric(PerformanceMetricType.TRIANGLES, '三角形数量', 0, '', 1000000);
    this.registerMetric(PerformanceMetricType.VERTICES, '顶点数量', 0, '', 2000000);

    // 物理指标
    this.registerMetric(PerformanceMetricType.COLLISION_PAIRS, '碰撞对数量', 0, '', 1000);
    this.registerMetric(PerformanceMetricType.CONTACT_POINTS, '接触点数量', 0, '', 5000);

    // 系统指标
    this.registerMetric(PerformanceMetricType.ENTITY_COUNT, '实体数量', 0, '', 5000);
    this.registerMetric(PerformanceMetricType.COMPONENT_COUNT, '组件数量', 0, '', 20000);
    this.registerMetric(PerformanceMetricType.SYSTEM_COUNT, '系统数量', 0, '', 50);

    // GPU指标
    this.registerMetric(PerformanceMetricType.GPU_USAGE, 'GPU使用率', 0, '%', 90);
    this.registerMetric(PerformanceMetricType.GPU_MEMORY, 'GPU内存使用', 0, 'MB', 1000);

    // CPU指标
    this.registerMetric(PerformanceMetricType.CPU_USAGE, 'CPU使用率', 0, '%', 80);

    // 资源指标
    this.registerMetric(PerformanceMetricType.RESOURCE_LOAD_TIME, '资源加载时间', 0, 'ms', 1000);
    this.registerMetric(PerformanceMetricType.RESOURCE_COUNT, '资源数量', 0, '', 1000);
    this.registerMetric(PerformanceMetricType.RESOURCE_MEMORY, '资源内存使用', 0, 'MB', 300);

    // 着色器指标
    this.registerMetric(PerformanceMetricType.SHADER_COMPILE_TIME, '着色器编译时间', 0, 'ms', 500);
    this.registerMetric(PerformanceMetricType.SHADER_COUNT, '着色器数量', 0, '', 100);

    // 材质和纹理指标
    this.registerMetric(PerformanceMetricType.MATERIAL_COUNT, '材质数量', 0, '', 500);
    this.registerMetric(PerformanceMetricType.TEXTURE_COUNT, '纹理数量', 0, '', 200);
    this.registerMetric(PerformanceMetricType.GEOMETRY_COUNT, '几何体数量', 0, '', 500);

    // 光照指标
    this.registerMetric(PerformanceMetricType.LIGHT_COUNT, '光源数量', 0, '', 50);
    this.registerMetric(PerformanceMetricType.SHADOW_MAP_COUNT, '阴影贴图数量', 0, '', 10);

    // 后处理指标
    this.registerMetric(PerformanceMetricType.POST_PROCESS_PASS_COUNT, '后处理通道数量', 0, '', 5);
    this.registerMetric(PerformanceMetricType.POST_PROCESS_TIME, '后处理时间', 0, 'ms', 5);

    // 可见性指标
    this.registerMetric(PerformanceMetricType.VISIBLE_OBJECT_COUNT, '可见对象数量', 0, '', 2000);
    this.registerMetric(PerformanceMetricType.CULLED_OBJECT_COUNT, '剔除对象数量', 0, '', 5000);

    // 网络指标
    this.registerMetric(PerformanceMetricType.NETWORK_MESSAGE_COUNT, '网络消息数量', 0, '', 100);
    this.registerMetric(PerformanceMetricType.NETWORK_DATA_SIZE, '网络数据大小', 0, 'KB', 1000);
    this.registerMetric(PerformanceMetricType.NETWORK_LATENCY, '网络延迟', 0, 'ms', 100);

    // 事件指标
    this.registerMetric(PerformanceMetricType.EVENT_COUNT, '事件数量', 0, '', 1000);
    this.registerMetric(PerformanceMetricType.EVENT_PROCESSING_TIME, '事件处理时间', 0, 'ms', 5);

    // 垃圾回收指标
    this.registerMetric(PerformanceMetricType.GC_TIME, '垃圾回收时间', 0, 'ms', 50);
    this.registerMetric(PerformanceMetricType.GC_COUNT, '垃圾回收次数', 0, '', 10);
  }

  /**
   * 配置性能监控系统
   * @param config 配置
   */
  public configure(config: PerformanceMonitorConfig): void {
    this.config = {
      ...this.config,
      ...config,
    };

    if (this.config.debug) {
      Debug.log('性能监控', '配置已更新', this.config);
    }
  }

  /**
   * 启动监控
   */
  public start(): void {
    if (!this.config.enabled || this.running) {
      return;
    }

    this.running = true;
    this.frameCount = 0;
    this.lastFrameTime = performance.now();
    this.lastAnalysisTime = performance.now();

    // 启动自动采样
    if (this.config.autoSample) {
      this.startAutoSampling();
    }

    // 启动瓶颈检测
    if (this.config.enableBottleneckDetection) {
      this.startBottleneckDetection();
    }

    // 启动趋势分析
    if (this.config.enableTrendAnalysis) {
      this.startTrendAnalysis();
    }

    // 启动报告导出
    if (this.config.enableReportExport) {
      this.startReportExport();
    }

    // 启动远程监控
    if (this.config.enableRemoteMonitoring) {
      this.startRemoteMonitoring();
    }

    if (this.config.debug) {
      Debug.log('性能监控', '监控已启动');
    }
  }

  /**
   * 停止监控
   */
  public stop(): void {
    if (!this.running) {
      return;
    }

    this.running = false;

    // 停止所有定时器
    this.stopAllTimers();

    if (this.config.debug) {
      Debug.log('性能监控', '监控已停止');
    }
  }

  /**
   * 停止所有定时器
   * @private
   */
  private stopAllTimers(): void {
    // 停止自动采样
    if (this.sampleTimerId !== null) {
      clearInterval(this.sampleTimerId);
      this.sampleTimerId = null;
    }

    // 停止瓶颈检测
    if (this.bottleneckDetectionTimerId !== null) {
      clearInterval(this.bottleneckDetectionTimerId);
      this.bottleneckDetectionTimerId = null;
    }

    // 停止趋势分析
    if (this.trendAnalysisTimerId !== null) {
      clearInterval(this.trendAnalysisTimerId);
      this.trendAnalysisTimerId = null;
    }

    // 停止报告导出
    if (this.reportExportTimerId !== null) {
      clearInterval(this.reportExportTimerId);
      this.reportExportTimerId = null;
    }

    // 停止远程监控
    if (this.remoteMonitoringTimerId !== null) {
      clearInterval(this.remoteMonitoringTimerId);
      this.remoteMonitoringTimerId = null;
    }
  }

  /**
   * 启动自动采样
   */
  private startAutoSampling(): void {
    if (this.sampleTimerId !== null) {
      clearInterval(this.sampleTimerId);
    }

    this.sampleTimerId = window.setInterval(() => {
      this.sample();
    }, this.config.sampleInterval);
  }

  /**
   * 启动瓶颈检测
   */
  private startBottleneckDetection(): void {
    if (this.bottleneckDetectionTimerId !== null) {
      clearInterval(this.bottleneckDetectionTimerId);
    }

    this.bottleneckDetectionTimerId = window.setInterval(() => {
      this.detectBottlenecks();
    }, 5000); // 每5秒检测一次瓶颈
  }

  /**
   * 启动趋势分析
   */
  private startTrendAnalysis(): void {
    if (this.trendAnalysisTimerId !== null) {
      clearInterval(this.trendAnalysisTimerId);
    }

    this.trendAnalysisTimerId = window.setInterval(() => {
      this.analyzeTrends();
    }, 10000); // 每10秒分析一次趋势
  }

  /**
   * 启动报告导出
   */
  private startReportExport(): void {
    if (this.reportExportTimerId !== null) {
      clearInterval(this.reportExportTimerId);
    }

    this.reportExportTimerId = window.setInterval(() => {
      this.exportReport();
    }, this.config.reportExportInterval);
  }

  /**
   * 启动远程监控
   */
  private startRemoteMonitoring(): void {
    if (this.remoteMonitoringTimerId !== null) {
      clearInterval(this.remoteMonitoringTimerId);
    }

    this.remoteMonitoringTimerId = window.setInterval(() => {
      this.sendRemoteMonitoringData();
    }, this.config.remoteMonitoringSendInterval);
  }

  /**
   * 记录帧开始
   */
  public beginFrame(): void {
    if (!this.running) {
      return;
    }

    this.frameStartTime = performance.now();
    this.frameCount++;
  }

  /**
   * 记录帧结束
   */
  public endFrame(): void {
    if (!this.running) {
      return;
    }

    const now = performance.now();
    const frameDuration = now - this.frameStartTime;

    // 更新总更新时间指标
    this.updateMetric(PerformanceMetricType.TOTAL_UPDATE_TIME, frameDuration);
  }

  /**
   * 开始测量
   * @param name 测量名称
   */
  public beginMeasure(name: string): void {
    if (!this.running) {
      return;
    }

    this.measureStartTimes[name] = performance.now();
  }

  /**
   * 结束测量
   * @param name 测量名称
   * @param metricType 指标类型
   */
  public endMeasure(name: string, metricType: PerformanceMetricType = PerformanceMetricType.CUSTOM): void {
    if (!this.running || !this.measureStartTimes[name]) {
      return;
    }

    const duration = performance.now() - this.measureStartTimes[name];
    delete this.measureStartTimes[name];

    // 更新对应的指标
    if (metricType === PerformanceMetricType.CUSTOM) {
      this.updateCustomMetric(name, duration);
    } else {
      this.updateMetric(metricType, duration);
    }
  }

  /**
   * 采样
   */
  public sample(): void {
    if (!this.running) {
      return;
    }

    const now = performance.now();
    const elapsed = now - this.lastFrameTime;

    // 计算FPS
    const fps = (this.frameCount * 1000) / elapsed;
    this.updateMetric(PerformanceMetricType.FPS, fps);

    // 收集内存指标
    if (this.config.collectMemoryMetrics) {
      this.collectMemoryMetrics();
    }

    // 收集渲染指标
    if (this.config.collectRenderMetrics) {
      this.collectRenderMetrics();
    }

    // 收集系统指标
    if (this.config.collectSystemMetrics) {
      this.collectSystemMetrics();
    }

    // 收集GPU指标
    if (this.config.collectGPUMetrics) {
      this.collectGPUMetrics();
    }

    // 收集CPU指标
    if (this.config.collectCPUMetrics) {
      this.collectCPUMetrics();
    }

    // 收集网络指标
    if (this.config.collectNetworkMetrics) {
      this.collectNetworkMetrics();
    }

    // 收集资源指标
    if (this.config.collectResourceMetrics) {
      this.collectResourceMetrics();
    }

    // 收集事件指标
    if (this.config.collectEventMetrics) {
      this.collectEventMetrics();
    }

    // 收集垃圾回收指标
    if (this.config.collectGCMetrics) {
      this.collectGCMetrics();
    }

    // 重置帧计数和时间
    this.frameCount = 0;
    this.lastFrameTime = now;

    // 检查性能警告
    if (this.config.enableWarnings) {
      this.checkPerformanceWarnings();
    }

    // 保存性能报告到历史记录
    this.savePerformanceHistory();

    if (this.config.debug) {
      Debug.log('性能监控', `FPS: ${fps.toFixed(2)}, 评分: ${this.overallScore.toFixed(0)}, 状态: ${this.performanceStatus}`);
    }
  }

  /**
   * 保存性能报告到历史记录
   * @private
   */
  private savePerformanceHistory(): void {
    const report = this.getReport();
    this.performanceHistory.push(report);

    // 限制历史记录长度
    if (this.performanceHistory.length > 100) {
      this.performanceHistory.shift();
    }
  }

  /**
   * 收集内存指标
   */
  private collectMemoryMetrics(): void {
    // 在浏览器环境中
    if (typeof window !== 'undefined' && window.performance && (window.performance as any).memory) {
      const memory = (window.performance as any).memory;
      const memoryUsage = memory.usedJSHeapSize / (1024 * 1024);
      this.updateMetric(PerformanceMetricType.MEMORY_USAGE, memoryUsage);
    }
    // 在Node.js环境中
    else if (typeof process !== 'undefined' && process.memoryUsage) {
      const memoryUsage = process.memoryUsage();
      this.updateMetric(PerformanceMetricType.MEMORY_USAGE, memoryUsage.heapUsed / (1024 * 1024));
    }
  }

  /**
   * 收集渲染指标
   */
  private collectRenderMetrics(): void {
    // 这里需要从引擎获取渲染器信息
    // 在实际实现中，应该通过引擎API获取

    // 模拟数据，实际应该从引擎获取
    if (typeof window !== 'undefined' && (window as any).renderer) {
      const renderer = (window as any).renderer;

      if (renderer.info) {
        const info = renderer.info;

        // 更新绘制调用次数
        if (info.render && info.render.calls !== undefined) {
          this.updateMetric(PerformanceMetricType.DRAW_CALLS, info.render.calls);
        }

        // 更新三角形数量
        if (info.render && info.render.triangles !== undefined) {
          this.updateMetric(PerformanceMetricType.TRIANGLES, info.render.triangles);
        }

        // 更新顶点数量
        if (info.memory && info.memory.geometries !== undefined) {
          this.updateMetric(PerformanceMetricType.VERTICES, info.memory.geometries * 500); // 估算值
        }

        // 更新材质数量
        if (info.memory && info.memory.textures !== undefined) {
          this.updateMetric(PerformanceMetricType.TEXTURE_COUNT, info.memory.textures);
        }

        // 更新几何体数量
        if (info.memory && info.memory.geometries !== undefined) {
          this.updateMetric(PerformanceMetricType.GEOMETRY_COUNT, info.memory.geometries);
        }
      }
    }
  }

  /**
   * 收集系统指标
   */
  private collectSystemMetrics(): void {
    // 这里需要从引擎获取系统信息
    // 在实际实现中，应该通过引擎API获取

    // 模拟数据，实际应该从引擎获取
    if (typeof window !== 'undefined' && (window as any).engine) {
      const engine = (window as any).engine;

      // 更新实体数量
      if (engine.entityCount !== undefined) {
        this.updateMetric(PerformanceMetricType.ENTITY_COUNT, engine.entityCount);
      } else {
        // 模拟数据
        this.updateMetric(PerformanceMetricType.ENTITY_COUNT, 1000);
      }

      // 更新组件数量
      if (engine.componentCount !== undefined) {
        this.updateMetric(PerformanceMetricType.COMPONENT_COUNT, engine.componentCount);
      } else {
        // 模拟数据
        this.updateMetric(PerformanceMetricType.COMPONENT_COUNT, 5000);
      }

      // 更新系统数量
      if (engine.systemCount !== undefined) {
        this.updateMetric(PerformanceMetricType.SYSTEM_COUNT, engine.systemCount);
      } else {
        // 模拟数据
        this.updateMetric(PerformanceMetricType.SYSTEM_COUNT, 20);
      }
    }
  }

  /**
   * 收集GPU指标
   */
  private collectGPUMetrics(): void {
    // 在浏览器环境中，可以尝试使用WebGL扩展获取GPU信息
    if (typeof window !== 'undefined' && (window as any).renderer) {
      const renderer = (window as any).renderer;

      if (renderer.getContext && renderer.getContext()) {
        const gl = renderer.getContext();

        // 尝试获取GPU信息
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        if (debugInfo) {
          const vendor = gl.getParameter(debugInfo.UNMASKED_VENDOR_WEBGL);
          const rendererInfo = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);

          // 记录GPU信息到自定义数据
          this.updateCustomMetric('gpuVendor', 0, vendor);
          this.updateCustomMetric('gpuRenderer', 0, rendererInfo);
        }
      }

      // 模拟GPU使用率和内存
      // 实际应该通过更高级的API获取，如WebGPU或原生API
      this.updateMetric(PerformanceMetricType.GPU_USAGE, 60 + Math.random() * 30);
      this.updateMetric(PerformanceMetricType.GPU_MEMORY, 500 + Math.random() * 500);
    }
  }

  /**
   * 收集CPU指标
   */
  private collectCPUMetrics(): void {
    // 在浏览器环境中，可以使用performance.now()来估算CPU使用率
    if (typeof window !== 'undefined') {
      // 模拟CPU使用率
      // 实际应该通过更高级的API获取，如原生API
      const cpuUsage = 50 + Math.random() * 30;
      this.updateMetric(PerformanceMetricType.CPU_USAGE, cpuUsage);
    }
    // 在Node.js环境中
    else if (typeof process !== 'undefined' && process.cpuUsage) {
      const cpuUsage = process.cpuUsage();
      const totalUsage = (cpuUsage.user + cpuUsage.system) / 1000; // 转换为毫秒
      this.updateMetric(PerformanceMetricType.CPU_USAGE, totalUsage / 10); // 转换为百分比
    }
  }

  /**
   * 收集网络指标
   */
  private collectNetworkMetrics(): void {
    // 在浏览器环境中，可以使用Performance API获取网络信息
    if (typeof window !== 'undefined' && window.performance) {
      // 获取网络资源信息
      if (window.performance.getEntriesByType) {
        const resources = window.performance.getEntriesByType('resource');

        if (resources && resources.length > 0) {
          // 计算平均加载时间
          let totalLoadTime = 0;
          let totalSize = 0;

          resources.forEach((resource: any) => {
            if (resource.duration) {
              totalLoadTime += resource.duration;
            }

            if (resource.transferSize) {
              totalSize += resource.transferSize;
            }
          });

          const avgLoadTime = resources.length > 0 ? totalLoadTime / resources.length : 0;
          const totalSizeKB = totalSize / 1024;

          this.updateMetric(PerformanceMetricType.NETWORK_DATA_SIZE, totalSizeKB);
          this.updateCustomMetric('networkResourceCount', resources.length);
        }
      }

      // 模拟网络延迟和消息数量
      this.updateMetric(PerformanceMetricType.NETWORK_LATENCY, 50 + Math.random() * 100);
      this.updateMetric(PerformanceMetricType.NETWORK_MESSAGE_COUNT, 20 + Math.random() * 50);
    }
  }

  /**
   * 收集资源指标
   */
  private collectResourceMetrics(): void {
    // 在浏览器环境中，可以使用Performance API获取资源信息
    if (typeof window !== 'undefined') {
      // 模拟资源加载时间和数量
      this.updateMetric(PerformanceMetricType.RESOURCE_LOAD_TIME, 200 + Math.random() * 800);
      this.updateMetric(PerformanceMetricType.RESOURCE_COUNT, 100 + Math.random() * 200);
      this.updateMetric(PerformanceMetricType.RESOURCE_MEMORY, 100 + Math.random() * 200);

      // 如果有资源管理器，可以从中获取更准确的信息
      if ((window as any).resourceManager) {
        const resourceManager = (window as any).resourceManager;

        if (resourceManager.getResourceCount) {
          this.updateMetric(PerformanceMetricType.RESOURCE_COUNT, resourceManager.getResourceCount());
        }

        if (resourceManager.getResourceMemory) {
          this.updateMetric(PerformanceMetricType.RESOURCE_MEMORY, resourceManager.getResourceMemory() / (1024 * 1024));
        }
      }
    }
  }

  /**
   * 收集事件指标
   */
  private collectEventMetrics(): void {
    // 在实际实现中，应该从引擎的事件系统获取
    // 模拟事件数量和处理时间
    this.updateMetric(PerformanceMetricType.EVENT_COUNT, 50 + Math.random() * 100);
    this.updateMetric(PerformanceMetricType.EVENT_PROCESSING_TIME, 1 + Math.random() * 4);
  }

  /**
   * 收集垃圾回收指标
   */
  private collectGCMetrics(): void {
    // 在浏览器环境中，可以使用Performance API获取垃圾回收信息
    if (typeof window !== 'undefined' && window.performance) {
      // 尝试获取垃圾回收事件
      if (window.performance.getEntriesByType) {
        const gcEntries = window.performance.getEntriesByType('gc');

        if (gcEntries && gcEntries.length > 0) {
          let totalGCTime = 0;

          gcEntries.forEach((entry: any) => {
            if (entry.duration) {
              totalGCTime += entry.duration;
            }
          });

          this.updateMetric(PerformanceMetricType.GC_TIME, totalGCTime);
          this.updateMetric(PerformanceMetricType.GC_COUNT, gcEntries.length);
        } else {
          // 模拟垃圾回收数据
          this.updateMetric(PerformanceMetricType.GC_TIME, Math.random() * 20);
          this.updateMetric(PerformanceMetricType.GC_COUNT, Math.floor(Math.random() * 5));
        }
      }
    }
  }

  /**
   * 检查性能警告
   */
  private checkPerformanceWarnings(): void {
    // 检查FPS
    const fpsMetric = this.metrics[PerformanceMetricType.FPS];
    if (fpsMetric && fpsMetric.value < 30) {
      Debug.warn('性能监控', `低帧率警告: ${fpsMetric.value.toFixed(2)} FPS`);
    }

    // 检查内存使用
    const memoryMetric = this.metrics[PerformanceMetricType.MEMORY_USAGE];
    if (memoryMetric && memoryMetric.value > 500) {
      Debug.warn('性能监控', `高内存使用警告: ${memoryMetric.value.toFixed(2)} MB`);
    }

    // 检查渲染时间
    const renderTimeMetric = this.metrics[PerformanceMetricType.RENDER_TIME];
    if (renderTimeMetric && renderTimeMetric.value > 16) {
      Debug.warn('性能监控', `渲染时间过长警告: ${renderTimeMetric.value.toFixed(2)} ms`);
    }
  }

  /**
   * 注册指标
   * @param type 指标类型
   * @param name 指标名称
   * @param initialValue 初始值
   * @param unit 单位
   * @param threshold 阈值
   */
  public registerMetric(type: PerformanceMetricType, name: string, initialValue: number = 0, unit?: string, threshold?: number): void {
    this.metrics[type] = {
      type,
      name,
      value: initialValue,
      min: initialValue,
      max: initialValue,
      average: initialValue,
      history: [initialValue],
      historyLimit: this.config.historyLimit,
      unit,
      threshold,
      exceedsThreshold: threshold !== undefined ? initialValue > threshold : undefined,
    };
  }

  /**
   * 注册自定义指标
   * @param name 指标名称
   * @param initialValue 初始值
   * @param unit 单位
   * @param threshold 阈值
   */
  public registerCustomMetric(name: string, initialValue: number = 0, unit?: string, threshold?: number): void {
    const customType = `${PerformanceMetricType.CUSTOM}.${name}`;
    this.registerMetric(customType as PerformanceMetricType, name, initialValue, unit, threshold);
  }

  /**
   * 更新指标
   * @param type 指标类型
   * @param value 值
   */
  public updateMetric(type: PerformanceMetricType, value: number): void {
    if (!this.metrics[type]) {
      return;
    }

    const metric = this.metrics[type];
    metric.value = value;

    // 更新最小值和最大值
    if (value < metric.min) {
      metric.min = value;
    }
    if (value > metric.max) {
      metric.max = value;
    }

    // 添加到历史
    metric.history.push(value);
    if (metric.history.length > metric.historyLimit) {
      metric.history.shift();
    }

    // 计算平均值
    metric.average = metric.history.reduce((sum, val) => sum + val, 0) / metric.history.length;

    // 检查阈值
    if (metric.threshold !== undefined) {
      metric.exceedsThreshold = value > metric.threshold;
    }
  }

  /**
   * 更新自定义指标
   * @param name 指标名称
   * @param value 值
   * @param customData 自定义数据
   */
  public updateCustomMetric(name: string, value: number, customData?: any): void {
    const customType = `${PerformanceMetricType.CUSTOM}.${name}`;

    // 如果指标不存在，则注册
    if (!this.metrics[customType as PerformanceMetricType]) {
      this.registerCustomMetric(name, value);
      return;
    }

    this.updateMetric(customType as PerformanceMetricType, value);

    // 如果有自定义数据，存储到指标中
    if (customData !== undefined) {
      const metric = this.metrics[customType as PerformanceMetricType];
      if (metric) {
        (metric as any).customData = customData;
      }
    }
  }

  /**
   * 检测性能瓶颈
   * @private
   */
  private detectBottlenecks(): void {
    if (!this.running || !this.config.enableBottleneckDetection) {
      return;
    }

    // 清除旧的瓶颈
    this.bottlenecks = [];

    // 检查FPS瓶颈
    this.detectFPSBottleneck();

    // 检查CPU瓶颈
    this.detectCPUBottleneck();

    // 检查GPU瓶颈
    this.detectGPUBottleneck();

    // 检查内存瓶颈
    this.detectMemoryBottleneck();

    // 检查渲染瓶颈
    this.detectRenderingBottleneck();

    // 检查物理瓶颈
    this.detectPhysicsBottleneck();

    // 检查网络瓶颈
    this.detectNetworkBottleneck();

    // 检查资源瓶颈
    this.detectResourceBottleneck();

    // 更新性能评分
    this.updatePerformanceScore();

    if (this.config.debug && this.bottlenecks.length > 0) {
      Debug.warn('性能监控', `检测到 ${this.bottlenecks.length} 个性能瓶颈`);
      this.bottlenecks.forEach(bottleneck => {
        Debug.warn('性能监控', `瓶颈: ${bottleneck.type}, 严重程度: ${bottleneck.severity.toFixed(2)}, 描述: ${bottleneck.description}`);
      });
    }
  }

  /**
   * 检测FPS瓶颈
   * @private
   */
  private detectFPSBottleneck(): void {
    const fpsMetric = this.metrics[PerformanceMetricType.FPS];
    if (!fpsMetric) return;

    if (fpsMetric.value < 30) {
      const severity = 1 - (fpsMetric.value / 60);
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.RENDERING,
        severity: Math.min(1, Math.max(0, severity)),
        description: `低帧率: ${fpsMetric.value.toFixed(1)} FPS`,
        relatedMetrics: [PerformanceMetricType.FPS, PerformanceMetricType.RENDER_TIME, PerformanceMetricType.TOTAL_UPDATE_TIME],
        optimizationSuggestions: [
          '减少场景复杂度',
          '优化渲染管线',
          '减少绘制调用次数',
          '使用LOD技术',
          '优化着色器'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 检测CPU瓶颈
   * @private
   */
  private detectCPUBottleneck(): void {
    const cpuMetric = this.metrics[PerformanceMetricType.CPU_USAGE];
    if (!cpuMetric) return;

    if (cpuMetric.value > 80) {
      const severity = (cpuMetric.value - 80) / 20;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.CPU,
        severity: Math.min(1, Math.max(0, severity)),
        description: `CPU使用率过高: ${cpuMetric.value.toFixed(1)}%`,
        relatedMetrics: [
          PerformanceMetricType.CPU_USAGE,
          PerformanceMetricType.SCRIPT_TIME,
          PerformanceMetricType.PHYSICS_TIME
        ],
        optimizationSuggestions: [
          '优化脚本执行',
          '减少物理计算',
          '使用Web Worker',
          '减少每帧处理的实体数量',
          '优化算法复杂度'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 检测GPU瓶颈
   * @private
   */
  private detectGPUBottleneck(): void {
    const gpuMetric = this.metrics[PerformanceMetricType.GPU_USAGE];
    if (!gpuMetric) return;

    if (gpuMetric.value > 90) {
      const severity = (gpuMetric.value - 90) / 10;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.GPU,
        severity: Math.min(1, Math.max(0, severity)),
        description: `GPU使用率过高: ${gpuMetric.value.toFixed(1)}%`,
        relatedMetrics: [
          PerformanceMetricType.GPU_USAGE,
          PerformanceMetricType.RENDER_TIME,
          PerformanceMetricType.DRAW_CALLS
        ],
        optimizationSuggestions: [
          '减少绘制调用次数',
          '简化着色器',
          '减少后处理效果',
          '降低渲染分辨率',
          '使用更简单的材质'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 检测内存瓶颈
   * @private
   */
  private detectMemoryBottleneck(): void {
    const memoryMetric = this.metrics[PerformanceMetricType.MEMORY_USAGE];
    if (!memoryMetric) return;

    if (memoryMetric.value > 500) {
      const severity = (memoryMetric.value - 500) / 500;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.MEMORY,
        severity: Math.min(1, Math.max(0, severity)),
        description: `内存使用过高: ${memoryMetric.value.toFixed(1)} MB`,
        relatedMetrics: [
          PerformanceMetricType.MEMORY_USAGE,
          PerformanceMetricType.TEXTURE_MEMORY,
          PerformanceMetricType.GEOMETRY_MEMORY
        ],
        optimizationSuggestions: [
          '减少纹理大小和数量',
          '优化几何体',
          '实现资源池',
          '使用资源卸载策略',
          '检查内存泄漏'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 检测渲染瓶颈
   * @private
   */
  private detectRenderingBottleneck(): void {
    const renderTimeMetric = this.metrics[PerformanceMetricType.RENDER_TIME];
    const drawCallsMetric = this.metrics[PerformanceMetricType.DRAW_CALLS];

    if (!renderTimeMetric || !drawCallsMetric) return;

    if (renderTimeMetric.value > 16) {
      const severity = (renderTimeMetric.value - 16) / 16;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.RENDERING,
        severity: Math.min(1, Math.max(0, severity)),
        description: `渲染时间过长: ${renderTimeMetric.value.toFixed(1)} ms`,
        relatedMetrics: [
          PerformanceMetricType.RENDER_TIME,
          PerformanceMetricType.DRAW_CALLS,
          PerformanceMetricType.TRIANGLES
        ],
        optimizationSuggestions: [
          '减少绘制调用次数',
          '使用实例化渲染',
          '优化几何体',
          '使用更简单的着色器',
          '实现视锥体剔除'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }

    if (drawCallsMetric.value > 1000) {
      const severity = (drawCallsMetric.value - 1000) / 1000;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.RENDERING,
        severity: Math.min(1, Math.max(0, severity)),
        description: `绘制调用次数过多: ${drawCallsMetric.value.toFixed(0)}`,
        relatedMetrics: [
          PerformanceMetricType.DRAW_CALLS,
          PerformanceMetricType.RENDER_TIME
        ],
        optimizationSuggestions: [
          '合并网格',
          '使用材质批处理',
          '使用实例化渲染',
          '减少场景复杂度'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 检测物理瓶颈
   * @private
   */
  private detectPhysicsBottleneck(): void {
    const physicsTimeMetric = this.metrics[PerformanceMetricType.PHYSICS_TIME];
    if (!physicsTimeMetric) return;

    if (physicsTimeMetric.value > 8) {
      const severity = (physicsTimeMetric.value - 8) / 8;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.PHYSICS,
        severity: Math.min(1, Math.max(0, severity)),
        description: `物理计算时间过长: ${physicsTimeMetric.value.toFixed(1)} ms`,
        relatedMetrics: [
          PerformanceMetricType.PHYSICS_TIME,
          PerformanceMetricType.COLLISION_PAIRS,
          PerformanceMetricType.CONTACT_POINTS
        ],
        optimizationSuggestions: [
          '减少物理对象数量',
          '使用简化的碰撞形状',
          '增加物理时间步长',
          '使用空间分区优化碰撞检测',
          '对远处物体禁用物理'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 检测网络瓶颈
   * @private
   */
  private detectNetworkBottleneck(): void {
    const networkLatencyMetric = this.metrics[PerformanceMetricType.NETWORK_LATENCY];
    const networkTimeMetric = this.metrics[PerformanceMetricType.NETWORK_TIME];

    if (!networkLatencyMetric || !networkTimeMetric) return;

    if (networkLatencyMetric.value > 100) {
      const severity = (networkLatencyMetric.value - 100) / 400;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.NETWORK,
        severity: Math.min(1, Math.max(0, severity)),
        description: `网络延迟过高: ${networkLatencyMetric.value.toFixed(0)} ms`,
        relatedMetrics: [
          PerformanceMetricType.NETWORK_LATENCY,
          PerformanceMetricType.NETWORK_TIME,
          PerformanceMetricType.NETWORK_DATA_SIZE
        ],
        optimizationSuggestions: [
          '减少网络请求频率',
          '压缩网络数据',
          '使用预测和插值',
          '实现客户端权威模型',
          '优化同步策略'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 检测资源瓶颈
   * @private
   */
  private detectResourceBottleneck(): void {
    const resourceLoadTimeMetric = this.metrics[PerformanceMetricType.RESOURCE_LOAD_TIME];
    if (!resourceLoadTimeMetric) return;

    if (resourceLoadTimeMetric.value > 1000) {
      const severity = (resourceLoadTimeMetric.value - 1000) / 4000;
      const bottleneck: PerformanceBottleneck = {
        type: PerformanceBottleneckType.RESOURCES,
        severity: Math.min(1, Math.max(0, severity)),
        description: `资源加载时间过长: ${resourceLoadTimeMetric.value.toFixed(0)} ms`,
        relatedMetrics: [
          PerformanceMetricType.RESOURCE_LOAD_TIME,
          PerformanceMetricType.RESOURCE_COUNT
        ],
        optimizationSuggestions: [
          '压缩资源',
          '实现资源流式加载',
          '使用资源缓存',
          '预加载关键资源',
          '减少资源大小和数量'
        ]
      };
      this.bottlenecks.push(bottleneck);
    }
  }

  /**
   * 更新性能评分
   * @private
   */
  private updatePerformanceScore(): void {
    // 如果没有瓶颈，评分为100
    if (this.bottlenecks.length === 0) {
      this.overallScore = 100;
      this.performanceStatus = 'excellent';
      return;
    }

    // 根据瓶颈严重程度计算评分
    let totalSeverity = 0;
    this.bottlenecks.forEach(bottleneck => {
      totalSeverity += bottleneck.severity;
    });

    // 计算平均严重程度
    const avgSeverity = totalSeverity / this.bottlenecks.length;

    // 计算评分 (100 - 严重程度 * 100)
    this.overallScore = Math.max(0, Math.min(100, 100 - avgSeverity * 100));

    // 更新性能状态
    if (this.overallScore >= 90) {
      this.performanceStatus = 'excellent';
    } else if (this.overallScore >= 75) {
      this.performanceStatus = 'good';
    } else if (this.overallScore >= 50) {
      this.performanceStatus = 'fair';
    } else if (this.overallScore >= 25) {
      this.performanceStatus = 'poor';
    } else {
      this.performanceStatus = 'critical';
    }
  }

  /**
   * 分析性能趋势
   * @private
   */
  private analyzeTrends(): void {
    if (!this.running || !this.config.enableTrendAnalysis) {
      return;
    }

    // 清除旧的趋势
    this.trends = [];

    // 分析关键指标的趋势
    this.analyzeMetricTrend(PerformanceMetricType.FPS);
    this.analyzeMetricTrend(PerformanceMetricType.MEMORY_USAGE);
    this.analyzeMetricTrend(PerformanceMetricType.CPU_USAGE);
    this.analyzeMetricTrend(PerformanceMetricType.GPU_USAGE);
    this.analyzeMetricTrend(PerformanceMetricType.RENDER_TIME);
    this.analyzeMetricTrend(PerformanceMetricType.PHYSICS_TIME);
    this.analyzeMetricTrend(PerformanceMetricType.NETWORK_LATENCY);

    if (this.config.debug && this.trends.length > 0) {
      Debug.log('性能监控', `分析了 ${this.trends.length} 个性能趋势`);
      this.trends.forEach(trend => {
        Debug.log('性能监控', `趋势: ${trend.metricType}, 类型: ${trend.type}, 变化率: ${trend.changeRate.toFixed(2)}`);
      });
    }
  }

  /**
   * 分析指标趋势
   * @param metricType 指标类型
   * @private
   */
  private analyzeMetricTrend(metricType: PerformanceMetricType): void {
    const metric = this.metrics[metricType];
    if (!metric || metric.history.length < 10) {
      return;
    }

    // 获取历史数据
    const history = metric.history;
    const historyLength = history.length;

    // 计算最近的平均值和较早的平均值
    const recentValues = history.slice(historyLength - 5);
    const earlierValues = history.slice(historyLength - 10, historyLength - 5);

    const recentAvg = recentValues.reduce((sum, val) => sum + val, 0) / recentValues.length;
    const earlierAvg = earlierValues.reduce((sum, val) => sum + val, 0) / earlierValues.length;

    // 计算变化率
    let changeRate = 0;
    if (earlierAvg !== 0) {
      changeRate = (recentAvg - earlierAvg) / earlierAvg;
    }

    // 计算标准差，用于判断波动性
    const stdDev = this.calculateStandardDeviation(history);
    const variationCoefficient = stdDev / recentAvg;

    // 确定趋势类型
    let trendType: PerformanceTrendType;

    if (variationCoefficient > 0.2) {
      trendType = PerformanceTrendType.FLUCTUATING;
    } else if (Math.abs(changeRate) < 0.05) {
      trendType = PerformanceTrendType.STABLE;
    } else {
      // 对于FPS，增加是改善，对于其他大多数指标，减少是改善
      const isImproving = metricType === PerformanceMetricType.FPS ? changeRate > 0 : changeRate < 0;
      trendType = isImproving ? PerformanceTrendType.IMPROVING : PerformanceTrendType.DEGRADING;
    }

    // 创建趋势对象
    const trend: PerformanceTrend = {
      type: trendType,
      metricType,
      changeRate,
      startTime: this.lastAnalysisTime,
      duration: Date.now() - this.lastAnalysisTime
    };

    this.trends.push(trend);
  }

  /**
   * 计算标准差
   * @param values 数值数组
   * @private
   */
  private calculateStandardDeviation(values: number[]): number {
    const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
    const squareDiffs = values.map(value => {
      const diff = value - avg;
      return diff * diff;
    });
    const avgSquareDiff = squareDiffs.reduce((sum, val) => sum + val, 0) / squareDiffs.length;
    return Math.sqrt(avgSquareDiff);
  }

  /**
   * 导出性能报告
   * @private
   */
  private exportReport(): void {
    if (!this.running || !this.config.enableReportExport) {
      return;
    }

    const report = this.getReport();

    // 在实际实现中，这里应该将报告保存到文件或发送到服务器
    if (this.config.debug) {
      Debug.log('性能监控', '导出性能报告', report);
    }
  }

  /**
   * 发送远程监控数据
   * @private
   */
  private sendRemoteMonitoringData(): void {
    if (!this.running || !this.config.enableRemoteMonitoring) {
      return;
    }

    const report = this.getReport();

    // 在实际实现中，这里应该将报告发送到远程服务器
    if (this.config.debug) {
      Debug.log('性能监控', '发送远程监控数据', report);
    }
  }

  /**
   * 获取性能报告
   * @returns 性能报告
   */
  public getReport(): PerformanceReport {
    return {
      timestamp: Date.now(),
      metrics: { ...this.metrics },
      bottlenecks: [...this.bottlenecks],
      trends: [...this.trends],
      overallScore: this.overallScore,
      status: this.performanceStatus,
      customData: {}
    };
  }

  /**
   * 获取指标
   * @param type 指标类型
   * @returns 指标
   */
  public getMetric(type: PerformanceMetricType): PerformanceMetric | undefined {
    return this.metrics[type];
  }

  /**
   * 获取自定义指标
   * @param name 指标名称
   * @returns 指标
   */
  public getCustomMetric(name: string): PerformanceMetric | undefined {
    const customType = `${PerformanceMetricType.CUSTOM}.${name}`;
    return this.metrics[customType as PerformanceMetricType];
  }

  /**
   * 重置指标
   * @param type 指标类型
   */
  public resetMetric(type: PerformanceMetricType): void {
    if (!this.metrics[type]) {
      return;
    }

    const metric = this.metrics[type];
    metric.value = 0;
    metric.min = 0;
    metric.max = 0;
    metric.average = 0;
    metric.history = [0];
    metric.exceedsThreshold = metric.threshold !== undefined ? 0 > metric.threshold : undefined;
  }

  /**
   * 重置所有指标
   */
  public resetAllMetrics(): void {
    for (const type in this.metrics) {
      this.resetMetric(type as PerformanceMetricType);
    }
  }

  /**
   * 清除历史数据
   * @param type 指标类型
   */
  public clearHistory(type: PerformanceMetricType): void {
    if (!this.metrics[type]) {
      return;
    }

    const metric = this.metrics[type];
    metric.history = [metric.value];
    metric.average = metric.value;
  }

  /**
   * 清除所有历史数据
   */
  public clearAllHistory(): void {
    for (const type in this.metrics) {
      this.clearHistory(type as PerformanceMetricType);
    }
  }

  // 静态方法，方便直接调用

  /**
   * 启动监控
   */
  public static start(): void {
    PerformanceMonitor.getInstance().start();
  }

  /**
   * 停止监控
   */
  public static stop(): void {
    PerformanceMonitor.getInstance().stop();
  }

  /**
   * 记录帧开始
   */
  public static beginFrame(): void {
    PerformanceMonitor.getInstance().beginFrame();
  }

  /**
   * 记录帧结束
   */
  public static endFrame(): void {
    PerformanceMonitor.getInstance().endFrame();
  }

  /**
   * 开始测量
   * @param name 测量名称
   */
  public static beginMeasure(name: string): void {
    PerformanceMonitor.getInstance().beginMeasure(name);
  }

  /**
   * 结束测量
   * @param name 测量名称
   * @param metricType 指标类型
   */
  public static endMeasure(name: string, metricType?: PerformanceMetricType): void {
    PerformanceMonitor.getInstance().endMeasure(name, metricType);
  }

  /**
   * 采样
   */
  public static sample(): void {
    PerformanceMonitor.getInstance().sample();
  }

  /**
   * 获取性能报告
   * @returns 性能报告
   */
  public static getReport(): PerformanceReport {
    return PerformanceMonitor.getInstance().getReport();
  }
}
