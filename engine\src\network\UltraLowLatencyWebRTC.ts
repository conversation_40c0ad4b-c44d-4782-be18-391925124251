/**
 * 超低延迟WebRTC实现
 * 目标：实现30ms以下的端到端延迟
 */
import { EventEmitter } from '../utils/EventEmitter';
import { Debug } from '../utils/Debug';

export interface UltraLowLatencyConfig {
  targetLatency: number;           // 目标延迟(ms)
  maxLatency: number;             // 最大可接受延迟(ms)
  adaptiveBuffering: boolean;     // 自适应缓冲
  hardwareAcceleration: boolean;  // 硬件加速
  simdOptimization: boolean;      // SIMD优化
  zeroCopyTransfer: boolean;      // 零拷贝传输
}

export interface LatencyMetrics {
  total: number;
  network: number;
  encoding: number;
  transmission: number;
  decoding: number;
  rendering: number;
  timestamp: number;
}

export class UltraLowLatencyWebRTC extends EventEmitter {
  private config: UltraLowLatencyConfig;
  private peerConnection: RTCPeerConnection | null = null;
  private dataChannel: RTCDataChannel | null = null;
  private latencyMonitor: LatencyMonitor;
  private adaptiveManager: AdaptiveManager;
  private hardwareAccelerator: HardwareAccelerator;
  private bufferManager: AdaptiveBufferManager;

  constructor(config: Partial<UltraLowLatencyConfig> = {}) {
    super();
    
    this.config = {
      targetLatency: 20,
      maxLatency: 30,
      adaptiveBuffering: true,
      hardwareAcceleration: true,
      simdOptimization: true,
      zeroCopyTransfer: true,
      ...config
    };

    this.latencyMonitor = new LatencyMonitor();
    this.adaptiveManager = new AdaptiveManager();
    this.hardwareAccelerator = new HardwareAccelerator();
    this.bufferManager = new AdaptiveBufferManager();
  }

  async initialize(): Promise<void> {
    // 初始化硬件加速
    if (this.config.hardwareAcceleration) {
      await this.hardwareAccelerator.initialize();
    }

    // 初始化自适应缓冲管理器
    if (this.config.adaptiveBuffering) {
      await this.bufferManager.initialize();
    }

    // 创建优化的PeerConnection
    await this.createOptimizedPeerConnection();

    Debug.log('UltraLowLatencyWebRTC', '超低延迟WebRTC初始化完成');
  }

  private async createOptimizedPeerConnection(): Promise<void> {
    const configuration: RTCConfiguration = {
      iceServers: [
        { urls: 'stun:stun.local.dl-engine.com:3478' },
        { urls: 'stun:stun.l.google.com:19302' }
      ],

      // 优化ICE配置
      iceTransportPolicy: 'all',
      bundlePolicy: 'max-bundle',
      rtcpMuxPolicy: 'require',
      iceCandidatePoolSize: 10

      // 注意：enableDscp 不是标准 RTCConfiguration 属性
      // 在实际实现中可能需要通过其他方式启用 DSCP 标记
    };

    this.peerConnection = new RTCPeerConnection(configuration);
    
    // 设置事件监听器
    this.setupPeerConnectionListeners();
    
    // 创建超低延迟数据通道
    await this.createUltraLowLatencyDataChannel();
  }

  private async createUltraLowLatencyDataChannel(): Promise<void> {
    if (!this.peerConnection) return;

    this.dataChannel = this.peerConnection.createDataChannel('ultra-low-latency', {
      ordered: false,           // 无序传输减少延迟
      maxRetransmits: 0        // 不重传，避免延迟
      // 注意：priority 和 protocol 不是标准 RTCDataChannelInit 属性
      // 可以通过其他方式实现优先级控制
    });

    this.dataChannel.binaryType = 'arraybuffer';
    
    // 设置数据通道事件监听器
    this.setupDataChannelListeners();
  }

  private setupPeerConnectionListeners(): void {
    if (!this.peerConnection) return;

    this.peerConnection.oniceconnectionstatechange = () => {
      Debug.log('UltraLowLatencyWebRTC', `ICE连接状态: ${this.peerConnection?.iceConnectionState}`);
    };

    this.peerConnection.onconnectionstatechange = () => {
      Debug.log('UltraLowLatencyWebRTC', `连接状态: ${this.peerConnection?.connectionState}`);
      
      if (this.peerConnection?.connectionState === 'connected') {
        this.startLatencyMonitoring();
      }
    };

    this.peerConnection.ondatachannel = (event) => {
      const channel = event.channel;
      this.setupDataChannelListeners(channel);
    };
  }

  private setupDataChannelListeners(channel?: RTCDataChannel): void {
    const dataChannel = channel || this.dataChannel;
    if (!dataChannel) return;

    dataChannel.onopen = () => {
      Debug.log('UltraLowLatencyWebRTC', '数据通道已打开');
      this.emit('dataChannelOpen');
    };

    dataChannel.onmessage = (event) => {
      const timestamp = performance.now();
      this.handleIncomingMessage(event.data, timestamp);
    };

    dataChannel.onerror = (error) => {
      Debug.error('UltraLowLatencyWebRTC', '数据通道错误:', error);
      this.emit('dataChannelError', error);
    };
  }

  async sendData(data: ArrayBuffer): Promise<void> {
    if (!this.dataChannel || this.dataChannel.readyState !== 'open') {
      throw new Error('数据通道未就绪');
    }

    const timestamp = performance.now();
    
    // 添加时间戳到数据包
    const timestampedData = this.addTimestamp(data, timestamp);
    
    // 使用零拷贝传输（如果启用）
    if (this.config.zeroCopyTransfer) {
      await this.sendZeroCopyData(timestampedData);
    } else {
      this.dataChannel.send(timestampedData);
    }
  }

  private async sendZeroCopyData(data: ArrayBuffer): Promise<void> {
    // 实现零拷贝传输逻辑
    // 使用SharedArrayBuffer避免数据拷贝
    const sharedBuffer = new SharedArrayBuffer(data.byteLength + 8); // +8字节用于时间戳
    const view = new Uint8Array(sharedBuffer);
    const dataView = new Uint8Array(data);
    
    // 写入时间戳
    const timestampView = new Float64Array(sharedBuffer, 0, 1);
    timestampView[0] = performance.now();
    
    // 写入数据
    view.set(dataView, 8);
    
    // 发送共享缓冲区引用
    this.dataChannel?.send(sharedBuffer);
  }

  private addTimestamp(data: ArrayBuffer, timestamp: number): ArrayBuffer {
    const timestampedData = new ArrayBuffer(data.byteLength + 8);
    const timestampView = new Float64Array(timestampedData, 0, 1);
    const dataView = new Uint8Array(timestampedData, 8);
    
    timestampView[0] = timestamp;
    dataView.set(new Uint8Array(data));
    
    return timestampedData;
  }

  private handleIncomingMessage(data: ArrayBuffer, receiveTimestamp: number): void {
    // 提取发送时间戳
    const timestampView = new Float64Array(data, 0, 1);
    const sendTimestamp = timestampView[0];
    
    // 计算传输延迟
    const transmissionLatency = receiveTimestamp - sendTimestamp;
    
    // 提取实际数据
    const actualData = data.slice(8);
    
    // 更新延迟统计
    this.latencyMonitor.recordLatency(transmissionLatency);
    
    // 触发数据接收事件
    this.emit('dataReceived', actualData, {
      transmissionLatency,
      receiveTimestamp,
      sendTimestamp
    });
  }

  private startLatencyMonitoring(): void {
    // 每100ms测量一次延迟
    setInterval(async () => {
      const metrics = await this.measureLatency();
      
      // 检查是否超过目标延迟
      if (metrics.total > this.config.targetLatency) {
        await this.optimizeForLatency(metrics);
      }
      
      this.emit('latencyUpdate', metrics);
    }, 100);
  }

  private async measureLatency(): Promise<LatencyMetrics> {
    const startTime = performance.now();
    
    // 发送ping消息
    const pingData = new ArrayBuffer(8);
    const pingView = new Float64Array(pingData);
    pingView[0] = startTime;
    
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve({
          total: this.config.maxLatency,
          network: this.config.maxLatency,
          encoding: 0,
          transmission: 0,
          decoding: 0,
          rendering: 0,
          timestamp: performance.now()
        });
      }, this.config.maxLatency);

      const handlePong = (event: MessageEvent) => {
        const pongTime = performance.now();
        const pingTime = new Float64Array(event.data)[0];
        const rtt = pongTime - pingTime;
        
        clearTimeout(timeout);
        this.dataChannel?.removeEventListener('message', handlePong);
        
        resolve({
          total: rtt,
          network: rtt / 2,
          encoding: 0,
          transmission: rtt / 2,
          decoding: 0,
          rendering: 0,
          timestamp: pongTime
        });
      };

      this.dataChannel?.addEventListener('message', handlePong);
      this.dataChannel?.send(pingData);
    });
  }

  private async optimizeForLatency(metrics: LatencyMetrics): Promise<void> {
    Debug.log('UltraLowLatencyWebRTC', `延迟超标: ${metrics.total}ms，开始优化`);
    
    // 自适应缓冲区调整
    if (this.config.adaptiveBuffering) {
      await this.bufferManager.adaptBufferSize(metrics.total);
    }
    
    // 网络参数调整
    await this.adaptiveManager.adjustNetworkParameters(this.peerConnection!, metrics);
    
    // 编码参数优化
    await this.optimizeEncodingParameters();
  }

  private async optimizeEncodingParameters(): Promise<void> {
    if (!this.peerConnection) return;

    const senders = this.peerConnection.getSenders();
    
    for (const sender of senders) {
      if (sender.track) {
        const params = sender.getParameters();
        
        // 优化编码参数以减少延迟
        params.encodings?.forEach(encoding => {
          // 设置标准的编码参数
          if (encoding.priority !== undefined) {
            encoding.priority = 'high' as RTCPriorityType;
          }
          if (encoding.networkPriority !== undefined) {
            encoding.networkPriority = 'high' as RTCPriorityType;
          }
          encoding.maxFramerate = 60;

          // 注意：codec 属性不是标准 RTCRtpEncodingParameters 的一部分
          // 编解码器参数通常在 SDP 中配置
        });
        
        await sender.setParameters(params);
      }
    }
  }

  async createOffer(): Promise<RTCSessionDescriptionInit> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection未初始化');
    }

    const offer = await this.peerConnection.createOffer({
      offerToReceiveAudio: false,
      offerToReceiveVideo: false,
      iceRestart: false
    });

    await this.peerConnection.setLocalDescription(offer);
    return offer;
  }

  async handleAnswer(answer: RTCSessionDescriptionInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection未初始化');
    }

    await this.peerConnection.setRemoteDescription(answer);
  }

  async addIceCandidate(candidate: RTCIceCandidateInit): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('PeerConnection未初始化');
    }

    await this.peerConnection.addIceCandidate(candidate);
  }

  getLatencyStatistics(): any {
    return this.latencyMonitor.getStatistics();
  }

  close(): void {
    if (this.dataChannel) {
      this.dataChannel.close();
      this.dataChannel = null;
    }

    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }

    Debug.log('UltraLowLatencyWebRTC', '连接已关闭');
  }
}

// 辅助类的简化实现
class LatencyMonitor {
  private latencies: number[] = [];

  recordLatency(latency: number): void {
    this.latencies.push(latency);
    if (this.latencies.length > 100) {
      this.latencies.shift();
    }
  }

  getStatistics(): any {
    if (this.latencies.length === 0) return { avg: 0, min: 0, max: 0 };
    
    const sorted = [...this.latencies].sort((a, b) => a - b);
    return {
      avg: this.latencies.reduce((sum, lat) => sum + lat, 0) / this.latencies.length,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p95: sorted[Math.floor(sorted.length * 0.95)]
    };
  }
}

class AdaptiveManager {
  async adjustNetworkParameters(_pc: RTCPeerConnection, _metrics: LatencyMetrics): Promise<void> {
    // 实现网络参数自适应调整
    // 这里可以根据延迟指标调整网络参数
  }
}

class HardwareAccelerator {
  async initialize(): Promise<void> {
    // 初始化硬件加速
  }
}

class AdaptiveBufferManager {
  async initialize(): Promise<void> {
    // 初始化自适应缓冲管理
  }

  async adaptBufferSize(_latency: number): Promise<void> {
    // 根据延迟调整缓冲区大小
    // 这里可以根据延迟值动态调整缓冲区大小
  }
}
